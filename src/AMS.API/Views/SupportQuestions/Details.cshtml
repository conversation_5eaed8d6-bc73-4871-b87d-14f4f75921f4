@model SupportQuestionDetailsViewModel
@{
    ViewData["Title"] = "Support Question Details";
    Layout = "_DashboardLayout";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Support Question Details</h1>
        <p class="text-muted mb-0">
            Reference: <code>@Model.SupportQuestion.Id.ToString().Substring(0, 8).ToUpper()</code>
        </p>
    </div>
    <div>
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to List
        </a>
    </div>
</div>

<div class="row g-4">
    <!-- Question Details -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-chat-dots me-2"></i>
                    Question Details
                </h5>
                <span class="badge status-badge <EMAIL>().Replace(" ", "-")">
                    @Model.SupportQuestion.ProcessingStatus
                </span>
            </div>
            <div class="card-body">
                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <label class="form-label text-muted">Name</label>
                        <p class="form-control-plaintext">
                            <i class="bi bi-person me-2"></i>
                            @Model.SupportQuestion.Name
                        </p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">Email</label>
                        <p class="form-control-plaintext">
                            <i class="bi bi-envelope me-2"></i>
                            <a href="mailto:@Model.SupportQuestion.Email">@Model.SupportQuestion.Email</a>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">Created</label>
                        <p class="form-control-plaintext">
                            <i class="bi bi-calendar me-2"></i>
                            @Model.SupportQuestion.CreatedAt.ToString("MMMM dd, yyyy 'at' HH:mm")
                        </p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">Last Updated</label>
                        <p class="form-control-plaintext">
                            <i class="bi bi-clock-history me-2"></i>
                            @(Model.SupportQuestion.UpdatedAt?.ToString("MMMM dd, yyyy 'at' HH:mm") ?? "Never")
                        </p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">Question</label>
                    <div class="card bg-light">
                        <div class="card-body">
                            <p class="card-text">@Model.SupportQuestion.Body</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions Panel -->
    <div class="col-lg-4">
        @if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
        {
            <!-- Status Management -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-gear me-2"></i>
                        Status Management
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if (Model.SupportQuestion.ProcessingStatus != "In Progress")
                        {
                            <form asp-action="UpdateStatus" method="post" class="d-inline">
                                <input type="hidden" name="id" value="@Model.SupportQuestion.Id" />
                                <input type="hidden" name="status" value="In Progress" />
                                <button type="submit" class="btn btn-outline-info btn-sm w-100">
                                    <i class="bi bi-arrow-clockwise me-2"></i>
                                    Mark In Progress
                                </button>
                            </form>
                        }
                        
                        @if (Model.SupportQuestion.ProcessingStatus != "Resolved")
                        {
                            <form asp-action="UpdateStatus" method="post" class="d-inline">
                                <input type="hidden" name="id" value="@Model.SupportQuestion.Id" />
                                <input type="hidden" name="status" value="Resolved" />
                                <button type="submit" class="btn btn-outline-success btn-sm w-100">
                                    <i class="bi bi-check-circle me-2"></i>
                                    Mark Resolved
                                </button>
                            </form>
                        }
                        
                        @if (Model.SupportQuestion.ProcessingStatus != "Closed")
                        {
                            <form asp-action="UpdateStatus" method="post" class="d-inline">
                                <input type="hidden" name="id" value="@Model.SupportQuestion.Id" />
                                <input type="hidden" name="status" value="Closed" />
                                <button type="submit" class="btn btn-outline-secondary btn-sm w-100">
                                    <i class="bi bi-x-circle me-2"></i>
                                    Mark Closed
                                </button>
                            </form>
                        }
                        
                        @if (Model.SupportQuestion.ProcessingStatus != "Pending")
                        {
                            <form asp-action="UpdateStatus" method="post" class="d-inline">
                                <input type="hidden" name="id" value="@Model.SupportQuestion.Id" />
                                <input type="hidden" name="status" value="Pending" />
                                <button type="submit" class="btn btn-outline-warning btn-sm w-100">
                                    <i class="bi bi-clock-history me-2"></i>
                                    Mark Pending
                                </button>
                            </form>
                        }
                    </div>
                </div>
            </div>

            <!-- Assignment -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-person-gear me-2"></i>
                        Assignment
                    </h6>
                </div>
                <div class="card-body">
                    @if (Model.SupportQuestion.AssignedToUserId.HasValue)
                    {
                        <div class="alert alert-success">
                            <i class="bi bi-person-check me-2"></i>
                            <strong>Assigned to:</strong><br>
                            @{
                                var assignedUser = Model.AvailableUsers.FirstOrDefault(u => u.Id == Model.SupportQuestion.AssignedToUserId);
                            }
                            @(assignedUser?.FullName ?? "Unknown User")
                        </div>
                        
                        <form asp-action="Assign" method="post">
                            <input type="hidden" name="id" value="@Model.SupportQuestion.Id" />
                            <input type="hidden" name="assignedToUserId" value="" />
                            <button type="submit" class="btn btn-outline-warning btn-sm w-100">
                                <i class="bi bi-person-dash me-2"></i>
                                Unassign
                            </button>
                        </form>
                    }
                    else
                    {
                        <form asp-action="Assign" method="post">
                            <input type="hidden" name="id" value="@Model.SupportQuestion.Id" />
                            <div class="mb-3">
                                <select name="assignedToUserId" class="form-select form-select-sm" required>
                                    <option value="">Select user to assign...</option>
                                    @foreach (var user in Model.AvailableUsers.Where(u => u.IsActive))
                                    {
                                        <option value="@user.Id">@user.FullName (@user.Role)</option>
                                    }
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                <i class="bi bi-person-plus me-2"></i>
                                Assign
                            </button>
                        </form>
                    }
                </div>
            </div>
        }

        <!-- Contact Information -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-envelope me-2"></i>
                    Contact Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="mailto:@Model.SupportQuestion.Email?subject=Re: Support Question - @Model.SupportQuestion.Id.ToString().Substring(0, 8).ToUpper()" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-envelope me-2"></i>
                        Send Email
                    </a>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard('@Model.SupportQuestion.Email')">
                        <i class="bi bi-clipboard me-2"></i>
                        Copy Email
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                AMS.utils.showToast('Email address copied to clipboard!', 'success');
            }, function(err) {
                AMS.utils.showToast('Failed to copy email address', 'error');
            });
        }
    </script>
}
