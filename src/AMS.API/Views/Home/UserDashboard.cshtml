@model DashboardViewModel
@{
    ViewData["Title"] = "My Dashboard";
    Layout = "_DashboardLayout";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">My Dashboard</h1>
        <p class="text-muted mb-0">Welcome back, @User.Identity?.Name</p>
    </div>
    <div class="text-muted">
        <i class="bi bi-clock me-1"></i>
        Last updated: @DateTime.Now.ToString("MMM dd, yyyy HH:mm")
    </div>
</div>

<!-- User Statistics Cards -->
<div class="row g-4 mb-5">
    <div class="col-xl-6 col-md-6">
        <div class="card dashboard-card card-stats">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">My Questions</h5>
                        <span class="h2 font-weight-bold mb-0">@Model.RecentSupportQuestions.Count</span>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon text-primary">
                            <i class="bi bi-chat-dots"></i>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0 text-muted text-sm">
                    <span class="text-primary me-2">
                        <i class="bi bi-question-circle"></i> Support questions submitted
                    </span>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-xl-6 col-md-6">
        <div class="card dashboard-card card-stats success">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">Account Status</h5>
                        <span class="h2 font-weight-bold mb-0">Active</span>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon text-success">
                            <i class="bi bi-person-check"></i>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0 text-muted text-sm">
                    <span class="text-success me-2">
                        <i class="bi bi-check-circle"></i> Account is active
                    </span>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- User Content -->
<div class="row g-4 mb-5">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-chat-dots me-2"></i>
                    My Recent Support Questions
                </h5>
                <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    New Question
                </a>
            </div>
            <div class="card-body">
                @if (Model.RecentSupportQuestions.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var question in Model.RecentSupportQuestions)
                                {
                                    <tr>
                                        <td>
                                            <div class="text-truncate" style="max-width: 200px;">
                                                @(question.Body.Length > 50 ? question.Body.Substring(0, 50) + "..." : question.Body)
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge status-badge <EMAIL>().Replace(" ", "-")">
                                                @question.ProcessingStatus
                                            </span>
                                        </td>
                                        <td>@question.CreatedAt.ToString("MMM dd, yyyy")</td>
                                        <td>
                                            <a asp-controller="SupportQuestions" asp-action="Details" asp-route-id="@question.Id"
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2 mb-3">You haven't submitted any support questions yet</p>
                        <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            Submit Your First Question
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        Submit Support Question
                    </a>
                    
                    <a asp-controller="Account" asp-action="Profile" class="btn btn-outline-primary">
                        <i class="bi bi-person me-2"></i>
                        My Profile
                    </a>
                    
                    <a href="/swagger" class="btn btn-outline-info" target="_blank">
                        <i class="bi bi-code-slash me-2"></i>
                        API Documentation
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle me-2"></i>
                    Need Help?
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">
                    If you need assistance or have questions about using the system, don't hesitate to reach out.
                </p>
                <div class="d-grid">
                    <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-outline-secondary">
                        <i class="bi bi-headset me-2"></i>
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
