@model DashboardViewModel
@{
    ViewData["Title"] = "My Dashboard";
    Layout = "_DashboardLayout";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">My Dashboard</h1>
        <p class="text-muted mb-0">Welcome back, @User.Identity?.Name</p>
    </div>
    <div class="text-muted">
        <i class="bi bi-clock me-1"></i>
        Last updated: @DateTime.Now.ToString("MMM dd, yyyy HH:mm")
    </div>
</div>

@* <!-- User Statistics Cards -->
<div class="row g-4 mb-5">
    <div class="col-xl-3 col-md-6">
        <div class="card dashboard-card card-stats success">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title text-uppercase text-muted mb-0">Account Status</h5>
                        <span class="h2 font-weight-bold mb-0">Active</span>
                    </div>
                    <div class="col-auto">
                        <div class="stat-icon text-success">
                            <i class="bi bi-person-check"></i>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0 text-muted text-sm">
                    <span class="text-success me-2">
                        <i class="bi bi-check-circle"></i> Account is active
                    </span>
                </p>
            </div>
        </div>
    </div>
</div> *@
