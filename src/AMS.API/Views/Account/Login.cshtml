@model LoginViewModel
@{
    ViewData["Title"] = "Sign In";
    Layout = "_Layout";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="bi bi-building text-primary" style="font-size: 3rem;"></i>
                        <h2 class="h4 mt-2 mb-0">Welcome Back</h2>
                        <p class="text-muted">Sign in to your AMS account</p>
                    </div>

                    <form asp-action="Login" asp-route-returnUrl="@ViewData["ReturnUrl"]" method="post" data-loading="true" id="loginForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger d-none" role="alert" id="validationSummary"></div>

                        <div class="form-floating mb-3">
                            <input asp-for="Email" class="form-control" placeholder="<EMAIL>" autocomplete="email" required />
                            <label asp-for="Email"></label>
                            <span asp-validation-for="Email" class="text-danger"></span>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="form-floating mb-3 position-relative">
                            <input asp-for="Password" class="form-control" placeholder="Password" autocomplete="current-password" required />
                            <label asp-for="Password"></label>
                            <button type="button" class="btn btn-outline-secondary password-toggle" onclick="togglePassword()">
                                <i class="bi bi-eye" id="passwordToggleIcon"></i>
                            </button>
                            <span asp-validation-for="Password" class="text-danger"></span>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="form-check mb-3">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label">
                                Remember me on this device
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                Sign In
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="text-muted mb-2">Need help accessing your account?</p>
                        <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-question-circle me-1"></i>
                            Contact Support
                        </a>
                    </div>
                </div>
            </div>

            <!-- Demo Accounts Info -->
            <div class="card mt-4 border-primary">
                <div class="card-header bg-primary bg-opacity-10">
                    <h6 class="card-title mb-0 text-primary">
                        <i class="bi bi-person-gear me-2"></i>
                        Demo Accounts
                    </h6>
                </div>
                <div class="card-body">
                    <p class="card-text small text-muted mb-3">
                        <i class="bi bi-hand-index me-1"></i>
                        Click on any account below to auto-fill the login form:
                    </p>
                    <div class="row g-2">
                        <div class="col-12">
                            <div class="demo-account p-2 border rounded">
                                <strong class="text-danger">Administrator</strong><br>
                                <small class="text-muted">
                                    Email: <EMAIL><br>
                                    Password: Admin123!
                                </small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="demo-account p-2 border rounded">
                                <strong class="text-warning">Manager</strong><br>
                                <small class="text-muted">
                                    Email: <EMAIL><br>
                                    Password: Manager123!
                                </small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="demo-account p-2 border rounded">
                                <strong class="text-info">User</strong><br>
                                <small class="text-muted">
                                    Email: <EMAIL><br>
                                    Password: User123!
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        // Auto-fill demo credentials
        document.addEventListener('DOMContentLoaded', function() {
            const demoAccounts = document.querySelectorAll('.demo-account');
            demoAccounts.forEach(account => {
                account.style.cursor = 'pointer';
                account.addEventListener('click', function() {
                    const text = this.textContent;
                    const emailMatch = text.match(/Email: ([^\s]+)/);
                    const passwordMatch = text.match(/Password: ([^\s]+)/);

                    if (emailMatch && passwordMatch) {
                        const emailInput = document.getElementById('Email');
                        const passwordInput = document.getElementById('Password');

                        if (emailInput && passwordInput) {
                            emailInput.value = emailMatch[1];
                            passwordInput.value = passwordMatch[1];

                            // Trigger change events for validation
                            emailInput.dispatchEvent(new Event('change'));
                            passwordInput.dispatchEvent(new Event('change'));

                            // Show visual feedback
                            showLoginFeedback('Demo credentials filled in. Click Sign In to continue.', 'success');

                            // Highlight the sign in button
                            const signInBtn = document.querySelector('button[type="submit"]');
                            if (signInBtn) {
                                signInBtn.classList.add('btn-pulse');
                                setTimeout(() => {
                                    signInBtn.classList.remove('btn-pulse');
                                }, 2000);
                            }
                        }
                    }
                });
            });

            // Add form submission handling
            const loginForm = document.querySelector('#loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const emailInput = this.querySelector('#Email');
                    const passwordInput = this.querySelector('#Password');

                    // Basic client-side validation
                    let isValid = true;

                    if (!emailInput.value.trim()) {
                        showFieldError(emailInput, 'Email is required');
                        isValid = false;
                    } else if (!isValidEmail(emailInput.value)) {
                        showFieldError(emailInput, 'Please enter a valid email address');
                        isValid = false;
                    } else {
                        clearFieldError(emailInput);
                    }

                    if (!passwordInput.value.trim()) {
                        showFieldError(passwordInput, 'Password is required');
                        isValid = false;
                    } else {
                        clearFieldError(passwordInput);
                    }

                    if (!isValid) {
                        e.preventDefault();
                        return false;
                    }

                    // Show loading state
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Signing In...';
                        loginForm.classList.add('form-loading');
                    }
                });

                // Add real-time validation
                const emailInput = loginForm.querySelector('#Email');
                const passwordInput = loginForm.querySelector('#Password');

                if (emailInput) {
                    emailInput.addEventListener('blur', function() {
                        if (this.value.trim() && !isValidEmail(this.value)) {
                            showFieldError(this, 'Please enter a valid email address');
                        } else if (this.value.trim()) {
                            clearFieldError(this);
                        }
                    });

                    emailInput.addEventListener('input', function() {
                        if (this.classList.contains('is-invalid') && this.value.trim()) {
                            clearFieldError(this);
                        }
                    });
                }

                if (passwordInput) {
                    passwordInput.addEventListener('input', function() {
                        if (this.classList.contains('is-invalid') && this.value.trim()) {
                            clearFieldError(this);
                        }
                    });
                }
            }
        });

        // Helper functions
        function showLoginFeedback(message, type = 'info') {
            // Remove any existing feedback
            const existingFeedback = document.querySelector('.login-feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }

            // Create new feedback element
            const feedback = document.createElement('div');
            feedback.className = `alert alert-${type === 'success' ? 'success' : 'info'} alert-dismissible fade show login-feedback mt-3`;
            feedback.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            // Insert after the form
            const form = document.querySelector('form');
            if (form) {
                form.parentNode.insertBefore(feedback, form.nextSibling);

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    if (feedback && feedback.parentNode) {
                        feedback.remove();
                    }
                }, 3000);
            }
        }

        function isValidEmail(email) {
            // Simple but effective email validation
            const emailPattern = /^[a-zA-Z0-9._%+-]+@@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emailPattern.test(email);
        }

        function showFieldError(field, message) {
            field.classList.add('is-invalid');
            field.classList.remove('is-valid');

            const feedback = field.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.textContent = message;
            }
        }

        function clearFieldError(field) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');

            const feedback = field.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.textContent = '';
            }
        }

        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.getElementById('Password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // Auto-fill demo credentials from homepage
        function checkForDemoCredentials() {
            const demoEmail = sessionStorage.getItem('demoEmail');
            const demoPassword = sessionStorage.getItem('demoPassword');

            if (demoEmail && demoPassword) {
                const emailInput = document.getElementById('Email');
                const passwordInput = document.getElementById('Password');

                if (emailInput && passwordInput) {
                    emailInput.value = demoEmail;
                    passwordInput.value = demoPassword;

                    // Trigger change events for validation
                    emailInput.dispatchEvent(new Event('change'));
                    passwordInput.dispatchEvent(new Event('change'));

                    // Clear from session storage
                    sessionStorage.removeItem('demoEmail');
                    sessionStorage.removeItem('demoPassword');

                    // Show feedback
                    showLoginFeedback('Demo credentials have been filled in. Click Sign In to continue.', 'success');

                    // Highlight the sign in button
                    const signInBtn = document.querySelector('button[type="submit"]');
                    if (signInBtn) {
                        signInBtn.classList.add('btn-pulse');
                        setTimeout(() => {
                            signInBtn.classList.remove('btn-pulse');
                        }, 3000);
                    }
                }
            }
        }

        // Show validation summary if there are server-side errors
        window.addEventListener('load', function() {
            const validationSummary = document.getElementById('validationSummary');
            if (validationSummary && validationSummary.textContent.trim()) {
                validationSummary.classList.remove('d-none');
            }

            // Check for demo credentials
            checkForDemoCredentials();
        });
    </script>
}

<style>
.demo-account {
    transition: all 0.2s ease-in-out;
    border: 1px solid #dee2e6 !important;
}

.demo-account:hover {
    background-color: #f8f9fa;
    cursor: pointer;
    border-color: #0d6efd !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.demo-account:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: .65;
    transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Button pulse animation */
@@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

.btn-pulse {
    animation: pulse 1s infinite;
}

/* Loading state for form */
.form-loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Enhanced card styling */
.card {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Demo account role colors */
.demo-account strong.text-danger {
    color: #dc3545 !important;
}

.demo-account strong.text-warning {
    color: #fd7e14 !important;
}

.demo-account strong.text-info {
    color: #0dcaf0 !important;
}

/* Improved form validation */
.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

/* Password toggle button */
.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    border: none;
    background: transparent;
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.password-toggle:hover {
    color: #495057;
    background: transparent;
    border: none;
}

.password-toggle:focus {
    box-shadow: none;
    border: none;
    outline: none;
}
</style>
