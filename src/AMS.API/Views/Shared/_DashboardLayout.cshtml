<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - AMS Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-building"></i>
                    AMS
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-end">
                    <ul class="navbar-nav">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle me-2"></i>
                                    <span class="d-none d-md-inline">@User.Identity.Name</span>
                                    <span class="d-md-none">Account</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li class="dropdown-header">
                                        <small class="text-muted">Signed in as:</small><br>
                                        <strong>@User.Identity.Name</strong>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-box-arrow-right"></i> Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Login">
                                    <i class="bi bi-box-arrow-in-right"></i> Login
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Mobile sidebar toggle -->
    <button class="sidebar-toggle d-md-none" type="button" onclick="toggleSidebar()">
        <i class="bi bi-list"></i>
    </button>

    <!-- Sidebar overlay for mobile -->
    <div class="sidebar-overlay" onclick="closeSidebar()"></div>

    <div class="dashboard-layout">
        <!-- Sidebar -->
        <nav class="dashboard-sidebar" id="dashboardSidebar">
            <!-- Navigation Section -->
            <div class="sidebar-section">Navigation</div>
            <ul class="sidebar-nav">
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["action"]?.ToString() == "Dashboard" ? "active" : "")"
                    asp-controller="Home" asp-action="Dashboard">
                        <i class="bi bi-speedometer2"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "SupportQuestions" && ViewContext.RouteData.Values["action"]?.ToString() == "Create" ? "active" : "")"
                       asp-controller="SupportQuestions" asp-action="Create">
                        <i class="bi bi-plus-circle"></i>
                        Submit Question
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Account" && ViewContext.RouteData.Values["action"]?.ToString() == "Profile" ? "active" : "")"
                       asp-controller="Account" asp-action="Profile">
                        <i class="bi bi-person"></i>
                        My Profile
                    </a>
                </li>
            </ul>

            @if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
            {
                <div class="sidebar-divider"></div>
                <div class="sidebar-section">Management</div>
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "SupportQuestions" && ViewContext.RouteData.Values["action"]?.ToString() == "Index" ? "active" : "")"
                           asp-controller="SupportQuestions" asp-action="Index">
                            <i class="bi bi-chat-dots"></i>
                            Support Questions
                        </a>
                    </li>
                    @if (User.IsInRole("Administrator"))
                    {
                        <li class="nav-item">
                            <a class="nav-link @(ViewContext.RouteData.Values["controller"]?.ToString() == "Users" ? "active" : "")"
                               asp-controller="Users" asp-action="Index">
                                <i class="bi bi-people"></i>
                                User Management
                            </a>
                        </li>
                    }
                </ul>
            }

            <div class="sidebar-divider"></div>
            <div class="sidebar-section">Quick Actions</div>
            <ul class="sidebar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="/swagger" target="_blank">
                        <i class="bi bi-code-slash"></i>
                        API Documentation
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" asp-controller="Home" asp-action="Index">
                        <i class="bi bi-house"></i>
                        Back to Home
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main content -->
        <main class="dashboard-content">
            <!-- Alert messages -->
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            @if (TempData["InfoMessage"] != null)
            {
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle"></i> @TempData["InfoMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }
            
            @RenderBody()
        </main>
    </div>

    <footer class="footer text-muted">
        <div class="container-fluid">
            <div class="row py-3">
                <div class="col-md-6">
                    &copy; @DateTime.Now.Year - AMS
                </div>
                <div class="col-md-6 text-end">
                    <a href="/swagger" class="text-muted me-3" target="_blank">
                        <i class="bi bi-code-slash"></i> API Documentation
                    </a>
                    <span class="text-muted">Version 1.0</span>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    <script>
        // Sidebar toggle functionality for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
        }
        
        function closeSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            const overlay = document.querySelector('.sidebar-overlay');
            
            sidebar.classList.remove('show');
            overlay.classList.remove('show');
        }
        
        // Close sidebar when clicking on a link (mobile)
        document.querySelectorAll('.sidebar-nav .nav-link').forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth <= 768) {
                    closeSidebar();
                }
            });
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
