@model CreateUserViewModel
@{
    ViewData["Title"] = "Create User";
    Layout = "_DashboardLayout";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Create New User</h1>
        <p class="text-muted mb-0">Add a new user to the system</p>
    </div>
    <div>
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Users
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form asp-action="Create" method="post" data-loading="true">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="FirstName" class="form-control" placeholder="First Name" />
                                <label asp-for="FirstName"></label>
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="LastName" class="form-control" placeholder="Last Name" />
                                <label asp-for="LastName"></label>
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-floating">
                                <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                                <label asp-for="Email"></label>
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="Password" class="form-control" placeholder="Password" />
                                <label asp-for="Password"></label>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input asp-for="ConfirmPassword" class="form-control" placeholder="Confirm Password" />
                                <label asp-for="ConfirmPassword"></label>
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select asp-for="Role" class="form-select">
                                    @foreach (var role in CreateUserViewModel.AvailableRoles)
                                    {
                                        <option value="@role">@role</option>
                                    }
                                </select>
                                <label asp-for="Role"></label>
                                <span asp-validation-for="Role" class="text-danger"></span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 d-flex align-items-center">
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" />
                                <label asp-for="IsActive" class="form-check-label">
                                    User is active
                                </label>
                                <span asp-validation-for="IsActive" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- Role Information -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">
                            <i class="bi bi-info-circle me-2"></i>
                            Role Permissions
                        </h6>
                        <ul class="mb-0">
                            <li><strong>Administrator:</strong> Full system access including user management</li>
                            <li><strong>Manager:</strong> Can manage support questions and view reports</li>
                            <li><strong>User:</strong> Basic access to submit and view own support questions</li>
                        </ul>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-person-plus me-2"></i>
                            Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Password Requirements -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-shield-check me-2"></i>
                    Password Requirements
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>At least 8 characters long</li>
                    <li>Mix of uppercase and lowercase letters recommended</li>
                    <li>Include numbers and special characters for better security</li>
                    <li>Avoid common passwords or personal information</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        // Password strength indicator
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('Password');
            const confirmPasswordInput = document.getElementById('ConfirmPassword');
            
            if (passwordInput) {
                passwordInput.addEventListener('input', function() {
                    // Add visual feedback for password strength
                    const password = this.value;
                    const strength = calculatePasswordStrength(password);
                    
                    // Remove existing classes
                    this.classList.remove('is-valid', 'is-invalid');
                    
                    if (password.length > 0) {
                        if (strength >= 3) {
                            this.classList.add('is-valid');
                        } else if (strength >= 2) {
                            // Neutral state
                        } else {
                            this.classList.add('is-invalid');
                        }
                    }
                });
            }
            
            function calculatePasswordStrength(password) {
                let strength = 0;
                if (password.length >= 8) strength++;
                if (/[a-z]/.test(password)) strength++;
                if (/[A-Z]/.test(password)) strength++;
                if (/[0-9]/.test(password)) strength++;
                if (/[^A-Za-z0-9]/.test(password)) strength++;
                return strength;
            }
        });
    </script>
}
