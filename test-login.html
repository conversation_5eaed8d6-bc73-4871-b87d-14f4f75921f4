<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AMS Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d1e7dd;
            color: #0a3622;
            border: 1px solid #a3cfbb;
        }
        .error {
            background-color: #f8d7da;
            color: #58151c;
            border: 1px solid #f1aeb5;
        }
        .info {
            background-color: #d1ecf1;
            color: #055160;
            border: 1px solid #b8daff;
        }
        button {
            background-color: #0d6efd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0b5ed7;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .demo-accounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .demo-account {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .demo-account h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .demo-account .credentials {
            font-family: monospace;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>🔐 AMS Login Functionality Test</h1>
    
    <div class="test-container">
        <h2>Quick Login Test</h2>
        <p>This page tests the AMS login functionality with the demo accounts.</p>
        
        <div class="demo-accounts">
            <div class="demo-account">
                <h4 style="color: #dc3545;">👑 Administrator</h4>
                <div class="credentials">
                    Email: <EMAIL><br>
                    Password: Admin123!
                </div>
                <button onclick="testLogin('<EMAIL>', 'Admin123!', 'Administrator')">Test Admin Login</button>
            </div>
            
            <div class="demo-account">
                <h4 style="color: #fd7e14;">👨‍💼 Manager</h4>
                <div class="credentials">
                    Email: <EMAIL><br>
                    Password: Manager123!
                </div>
                <button onclick="testLogin('<EMAIL>', 'Manager123!', 'Manager')">Test Manager Login</button>
            </div>
            
            <div class="demo-account">
                <h4 style="color: #0dcaf0;">👤 User</h4>
                <div class="credentials">
                    Email: <EMAIL><br>
                    Password: User123!
                </div>
                <button onclick="testLogin('<EMAIL>', 'User123!', 'User')">Test User Login</button>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="testInvalidLogin()">Test Invalid Login</button>
            <button onclick="clearResults()">Clear Results</button>
            <button onclick="openAMS()">Open AMS Application</button>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test Results</h2>
        <div id="results"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:5001';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(result);
            results.scrollTop = results.scrollHeight;
        }
        
        async function testLogin(email, password, role) {
            addResult(`Testing login for ${role} (${email})...`, 'info');
            
            try {
                // First, get the login page to get the anti-forgery token
                const loginPageResponse = await fetch(`${baseUrl}/account/login`);
                if (!loginPageResponse.ok) {
                    throw new Error(`Failed to load login page: ${loginPageResponse.status}`);
                }
                
                const loginPageHtml = await loginPageResponse.text();
                
                // Extract the anti-forgery token
                const tokenMatch = loginPageHtml.match(/name="__RequestVerificationToken"[^>]*value="([^"]+)"/);
                if (!tokenMatch) {
                    throw new Error('Could not find anti-forgery token');
                }
                const token = tokenMatch[1];
                
                // Prepare form data
                const formData = new FormData();
                formData.append('Email', email);
                formData.append('Password', password);
                formData.append('RememberMe', 'false');
                formData.append('__RequestVerificationToken', token);
                
                // Submit login form
                const loginResponse = await fetch(`${baseUrl}/account/login`, {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    redirect: 'manual' // Don't follow redirects automatically
                });
                
                if (loginResponse.status === 302) {
                    // Successful login - check redirect location
                    const location = loginResponse.headers.get('Location');
                    if (location && (location.includes('/Dashboard') || location.includes('/Home'))) {
                        addResult(`✅ ${role} login successful! Redirected to: ${location}`, 'success');
                        
                        // Test accessing the dashboard
                        const dashboardResponse = await fetch(`${baseUrl}${location}`, {
                            credentials: 'include'
                        });
                        
                        if (dashboardResponse.ok) {
                            addResult(`✅ Dashboard access confirmed for ${role}`, 'success');
                        } else {
                            addResult(`⚠️ Dashboard access failed for ${role}: ${dashboardResponse.status}`, 'error');
                        }
                    } else {
                        addResult(`⚠️ ${role} login redirected to unexpected location: ${location}`, 'error');
                    }
                } else if (loginResponse.status === 200) {
                    // Login form returned - likely failed
                    const responseText = await loginResponse.text();
                    if (responseText.includes('alert-danger') || responseText.includes('validation-summary')) {
                        addResult(`❌ ${role} login failed - invalid credentials or validation error`, 'error');
                    } else {
                        addResult(`⚠️ ${role} login returned form without clear error`, 'error');
                    }
                } else {
                    addResult(`❌ ${role} login failed with status: ${loginResponse.status}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ ${role} login test failed: ${error.message}`, 'error');
            }
        }
        
        async function testInvalidLogin() {
            addResult('Testing invalid login credentials...', 'info');
            
            try {
                // Get login page for token
                const loginPageResponse = await fetch(`${baseUrl}/account/login`);
                const loginPageHtml = await loginPageResponse.text();
                const tokenMatch = loginPageHtml.match(/name="__RequestVerificationToken"[^>]*value="([^"]+)"/);
                const token = tokenMatch ? tokenMatch[1] : '';
                
                const formData = new FormData();
                formData.append('Email', '<EMAIL>');
                formData.append('Password', 'wrongpassword');
                formData.append('RememberMe', 'false');
                formData.append('__RequestVerificationToken', token);
                
                const loginResponse = await fetch(`${baseUrl}/account/login`, {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    redirect: 'manual'
                });
                
                if (loginResponse.status === 200) {
                    const responseText = await loginResponse.text();
                    if (responseText.includes('alert-danger') || responseText.includes('validation-summary')) {
                        addResult('✅ Invalid login correctly rejected with error message', 'success');
                    } else {
                        addResult('⚠️ Invalid login rejected but no clear error message shown', 'error');
                    }
                } else {
                    addResult(`⚠️ Invalid login test unexpected status: ${loginResponse.status}`, 'error');
                }
                
            } catch (error) {
                addResult(`❌ Invalid login test failed: ${error.message}`, 'error');
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function openAMS() {
            window.open(`${baseUrl}`, '_blank');
        }
        
        // Initial message
        addResult('Login test page loaded. Click buttons above to test login functionality.', 'info');
    </script>
</body>
</html>
